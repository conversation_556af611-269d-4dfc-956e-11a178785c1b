// API configuration
// Automatically select API URL for Android emulator or physical device
// Use ******** for Android emulator, otherwise use your local network IP for physical device
// To use on physical device, change LOCAL_NETWORK_IP below to your computer's IP address

function getApiUrl() {
  return `https://connecto-backend-l4xp.onrender.com/api`;
}

function getSocketUrl() {
  return `https://connecto-backend-l4xp.onrender.com`;
}

export const API_URL = getApiUrl();

// Log the configuration for debugging
console.log('🔧 Network Configuration:');
console.log('📡 API_URL:', API_URL);
console.log('🔌 SOCKET_URL:', getSocketUrl());
console.log('💡 If using physical device and getting connection errors:');
console.log(
  '   1. Make sure your phone and computer are on the same WiFi network',
);
console.log(
  "   2. Update LOCAL_NETWORK_IP in config.ts to your computer's IP address",
);
console.log('   3. Find your IP: Windows (ipconfig) | Mac/Linux (ifconfig)');
console.log('   4. Make sure your backend server is running on port 5000');

// Endpoints
export const ENDPOINTS = {
  // Auth
  SEND_OTP: '/auth/send-otp',
  VERIFY_OTP: '/auth/verify-otp',
  GET_ME: '/auth/me',
  UPDATE_GENDER: '/auth/gender',
  LOGOUT: '/auth/logout',

  // Users
  GET_USER: '/users',
  UPDATE_USER: '/users',
  GET_BALANCE: '/users/balance',
  ADD_BALANCE: '/users/add-balance',
  GET_TRANSACTIONS: '/users/transactions',

  // Experts
  GET_EXPERTS: '/experts',
  GET_EXPERT: '/experts',
  REGISTER_EXPERT: '/experts/register',
  UPDATE_EXPERT: '/experts',

  // Calls
  INITIATE_CALL: '/calls/initiate',
  ACCEPT_CALL: '/calls/accept',
  CONNECT_CALL: '/calls/connect',
  REJECT_CALL: '/calls/reject',
  MISSED_CALL: '/calls/missed',
  END_CALL: '/calls/end',
  UPLOAD_RECORDING: '/calls/recording',
  GET_CALL_HISTORY: '/calls/history',
  GET_CALL: '/calls',
};

// WebSocket URL
export const SOCKET_URL = getSocketUrl();
