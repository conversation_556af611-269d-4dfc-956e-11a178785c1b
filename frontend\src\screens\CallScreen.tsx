import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, Alert, ActivityIndicator, Platform } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Mi<PERSON>, MicOff, PhoneOff, Volume2 } from 'lucide-react-native';
import { callService, soundService } from '../services';
import { RTCPeerConnection, RTCIceCandidate, RTCSessionDescription, mediaDevices } from 'react-native-webrtc';
import { requestAudioPermissions } from '../utils/permissions';
import { debugNativeModules } from '../utils/moduleDebug';
// Try to import react-native-incall-manager with multiple approaches
let InCallManager: any = null;
let isInCallManagerAvailable = false;

// Approach 1: Direct require
try {
  InCallManager = require('react-native-incall-manager');
  if (InCallManager && typeof InCallManager.start === 'function') {
    isInCallManagerAvailable = true;
    console.log('✅ react-native-incall-manager loaded via direct require');
  } else if (InCallManager && InCallManager.default && typeof InCallManager.default.start === 'function') {
    // Some versions export as default
    InCallManager = InCallManager.default;
    isInCallManagerAvailable = true;
    console.log('✅ react-native-incall-manager loaded via default export');
  } else {
    throw new Error('Module loaded but start function not found');
  }
} catch (error) {
  console.warn('❌ Direct require failed:', error);

  // Approach 2: Try with NativeModules
  try {
    const { NativeModules } = require('react-native');
    if (NativeModules.InCallManager && typeof NativeModules.InCallManager.start === 'function') {
      InCallManager = NativeModules.InCallManager;
      isInCallManagerAvailable = true;
      console.log('✅ react-native-incall-manager loaded via NativeModules');
    } else {
      throw new Error('InCallManager not found in NativeModules');
    }
  } catch (nativeError) {
    console.warn('❌ NativeModules approach failed:', nativeError);
    isInCallManagerAvailable = false;
  }
}

console.log(`🔧 InCallManager final status: ${isInCallManagerAvailable ? 'AVAILABLE' : 'NOT AVAILABLE'}`);

// Create a safe wrapper that always has the required methods
const InCallManagerWrapper = {
  start: (options: { media?: string; auto?: boolean; ringback?: string }) => {
    console.log('🎯 InCallManagerWrapper.start called with:', options);
    console.log('🔍 isInCallManagerAvailable:', isInCallManagerAvailable);
    console.log('🔍 InCallManager object:', InCallManager);

    if (isInCallManagerAvailable && InCallManager) {
      try {
        console.log('🚀 Calling native InCallManager.start...');
        const result = InCallManager.start(options);
        console.log('✅ InCallManager.start completed, result:', result);
      } catch (error) {
        console.error('❌ Error calling InCallManager.start:', error);
        if (error instanceof Error) {
          console.error('❌ Error stack:', error.stack);
        }
      }
    } else {
      console.log('🎭 Mock InCallManager: start', options);
    }
  },
  stop: () => {
    if (isInCallManagerAvailable && InCallManager) {
      try {
        InCallManager.stop();
        console.log('InCallManager: stop');
      } catch (error) {
        console.error('Error calling InCallManager.stop:', error);
      }
    } else {
      console.log('Mock InCallManager: stop');
    }
  },
  setForceSpeakerphoneOn: (enabled: boolean) => {
    if (isInCallManagerAvailable && InCallManager) {
      try {
        InCallManager.setForceSpeakerphoneOn(enabled);
        console.log('InCallManager: setForceSpeakerphoneOn', enabled);
      } catch (error) {
        console.error('Error calling InCallManager.setForceSpeakerphoneOn:', error);
      }
    } else {
      console.log('Mock InCallManager: setForceSpeakerphoneOn', enabled);
    }
  },
  setMicrophoneMute: (muted: boolean) => {
    if (isInCallManagerAvailable && InCallManager) {
      try {
        InCallManager.setMicrophoneMute(muted);
        console.log('InCallManager: setMicrophoneMute', muted);
      } catch (error) {
        console.error('Error calling InCallManager.setMicrophoneMute:', error);
      }
    } else {
      console.log('Mock InCallManager: setMicrophoneMute', muted);
    }
  },
  setKeepScreenOn: (enabled: boolean) => {
    if (isInCallManagerAvailable && InCallManager) {
      try {
        InCallManager.setKeepScreenOn(enabled);
        console.log('InCallManager: setKeepScreenOn', enabled);
      } catch (error) {
        console.error('Error calling InCallManager.setKeepScreenOn:', error);
      }
    } else {
      console.log('Mock InCallManager: setKeepScreenOn', enabled);
    }
  },
  stopRingback: () => {
    if (isInCallManagerAvailable && InCallManager) {
      try {
        // Try different methods to stop ringback
        if (typeof InCallManager.stopRingback === 'function') {
          InCallManager.stopRingback();
          console.log('InCallManager: stopRingback');
        } else if (typeof InCallManager.stop === 'function') {
          // Stop and restart without ringback
          InCallManager.stop();
          setTimeout(() => {
            InCallManager.start({
              media: 'audio',
              auto: true,
              ringback: ''
            });
          }, 100);
          console.log('InCallManager: stopped and restarted without ringback');
        }
      } catch (error) {
        console.error('Error calling InCallManager.stopRingback:', error);
      }
    } else {
      console.log('Mock InCallManager: stopRingback');
    }
  },
};

// Define types for WebRTC events that are missing in the TypeScript definitions
interface RTCIceCandidateEvent {
  candidate: RTCIceCandidate | null;
}

type CallScreenParams = {
  CallScreen: {
    expertId: string;
    userName: string;
    category: string;
    rate: string;
    callId?: string;
    isIncoming?: boolean;
  };
};

// Extend RTCPeerConnection to include missing properties
declare module 'react-native-webrtc' {
  interface RTCPeerConnection {
    onicecandidate: ((event: RTCIceCandidateEvent) => void) | null;
    onconnectionstatechange: (() => void) | null;
    oniceconnectionstatechange: (() => void) | null;
    ontrack: ((event: any) => void) | null;
  }
}

const CallScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<CallScreenParams, 'CallScreen'>>();
  const { expertId, userName, category, rate, callId: existingCallId, isIncoming } = route.params;

  const [callDuration, setCallDuration] = useState(0);
  const [isCallActive, setIsCallActive] = useState(false);
  const [isConnecting, setIsConnecting] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const [callId, setCallId] = useState<string | null>(null);
  const [callStartTime, setCallStartTime] = useState<number | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const keepAliveRef = useRef<NodeJS.Timeout | null>(null);
  const isCallActiveRef = useRef<boolean>(false);

  // WebRTC
  const peerConnection = useRef<RTCPeerConnection | null>(null);
  const localStream = useRef<any>(null);

  // Format seconds to mm:ss
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Keep-alive mechanism to maintain connection
  const startKeepAlive = (activeCallId: string) => {
    console.log('🔄 Starting connection keep-alive for call:', activeCallId);
    keepAliveRef.current = setInterval(() => {
      if (peerConnection.current && callService.socket && activeCallId) {
        const connectionState = peerConnection.current.connectionState;
        const iceState = (peerConnection.current as any)?.iceConnectionState;
        console.log('💓 Keep-alive check - Connection:', connectionState, 'ICE:', iceState);

        // Send keep-alive ping through socket with correct callId
        callService.socket.emit('call-ping', { callId: activeCallId });
      }
    }, 10000); // Every 10 seconds
  };

  const stopKeepAlive = () => {
    if (keepAliveRef.current) {
      console.log('🛑 Stopping connection keep-alive');
      clearInterval(keepAliveRef.current);
      keepAliveRef.current = null;
    }
  };

  // Initialize call
  useEffect(() => {
    const initCall = async () => {
      try {
        // Debug native modules
        debugNativeModules();

        let callId: string = existingCallId || '';

        if (!isIncoming) {
          // Outgoing caller flow
          console.log('🔄 Initiating call to expert...');
          const initiateRes = await callService.initiateCall(expertId);
          if (!initiateRes.success || !initiateRes.data) {
            throw new Error(initiateRes.message || 'Failed to initiate call');
          }
          callId = initiateRes.data._id;
          console.log('✅ Call initiated with ID:', callId);
          setCallId(callId);

          // Start ringing for outgoing call (caller hears ringing while waiting for expert)
          soundService.startRinging();
        } else {
          setCallId(callId);
        }

        setIsConnecting(true);
        console.log('🔄 Starting WebRTC initialization...');

        // 2. Initialize socket connection with retry logic
        const socket = await callService.initSocket();
        if (!socket) {
          throw new Error('Failed to establish socket connection. Please check your network connection.');
        }

        // Set up audio session first
        await setupAudioSession();

        // 3. Initialize WebRTC (create peerConnection, get tracks)
        await initWebRTC(callId);

        // 4. Set up socket event listeners FIRST
        setupSocketListeners(callId);

        // 5. Create/join room after listeners are ready
        if (!isIncoming) {
          // Caller creates the room
          console.log('Caller creating room...');
          const roomCreated = await callService.webrtc.createRoom(callId);
          if (!roomCreated) {
            throw new Error('Failed to create signaling room. The expert may be unavailable.');
          }

          // Wait for expert to join room before sending offer
          console.log('Caller waiting for expert to join room...');
          const expertJoined = await new Promise((resolve) => {
            if (!callService.socket) return resolve(false);

            const timeout = setTimeout(() => {
              console.log('Timeout waiting for expert to join room');
              resolve(false);
            }, 30000); // Increased timeout to 30 seconds

            callService.socket.once('room-joined', () => {
              console.log('Expert joined room!');
              clearTimeout(timeout);
              // Add small delay to ensure expert is fully ready
              setTimeout(() => resolve(true), 1000);
            });
          });

          if (!expertJoined) {
            throw new Error('Expert did not join the call. They may be busy or unavailable.');
          }

          console.log('Creating and sending offer...');
          if (!peerConnection.current) {
            throw new Error('WebRTC connection not initialized');
          }

          const offer = await peerConnection.current.createOffer({
            offerToReceiveAudio: true,
            offerToReceiveVideo: false,
            voiceActivityDetection: true,
            iceRestart: false,
          });
          await peerConnection.current.setLocalDescription(offer);
          console.log('Offer created and local description set');
          callService.webrtc.sendOffer(callId, offer);
        } else {
          // Expert joins the existing room AFTER listeners are set up
          console.log('Expert joining room...');

          // Add small delay to ensure caller is ready
          await new Promise(resolve => setTimeout(resolve, 500));

          const roomJoined = await callService.webrtc.joinRoom(callId);
          if (!roomJoined) {
            throw new Error('Failed to join signaling room. The caller may have disconnected.');
          }

          // Mark call as connected and wait for offer
          console.log('Expert ready, marking call as connected...');
          if (callId) {
            const connectResult = await callService.connectToCall(callId);
            if (!connectResult.success) {
              console.warn('Failed to mark call as connected:', connectResult.message);
            }
          }

          // Expert backup timer - start timer if no sync received within 10 seconds
          setTimeout(() => {
            if (isIncoming && callDuration === 0) {
              console.log('🕐 Expert starting emergency backup timer (no sync received)');
              const startTime = Date.now();
              startCallTimer(startTime);
              setIsConnecting(false);
              setIsCallActive(true);
              soundService.forceStopRinging();
              InCallManagerWrapper.stopRingback();
            }
          }, 10000); // Wait 10 seconds for timer sync
        }

      } catch (error) {
        console.error('WebRTC initialization error:', error);

        // Stop ringing if call initialization fails
        soundService.stopRinging();

        const errorMessage = error instanceof Error ? error.message : 'Failed to initialize call';
        Alert.alert('Call Failed', errorMessage, [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]);
      }
    };

    initCall();

    return () => {
      // Clean up on unmount
      console.log('🧹 CallScreen cleanup triggered - checking if call is active');

      // Only cleanup if call is not active or if we're really unmounting
      const shouldCleanup = !isCallActiveRef.current || callDuration === 0;

      if (shouldCleanup) {
        console.log('🧹 Executing cleanup - call is not active');
        stopCallTimer(); // Stop the call timer
        stopKeepAlive(); // Stop keep-alive
        cleanupAudioSession(); // Clean up audio session
        soundService.forceStopRinging(); // Force stop any ringing sounds

        if (localStream.current) {
          console.log('🎤 Stopping local audio tracks');
          localStream.current.getTracks().forEach((track: any) => track.stop());
        }

        if (peerConnection.current) {
          console.log('🔌 Closing peer connection');
          peerConnection.current.close();
        }
      } else {
        console.log('🚫 Skipping cleanup - call is still active');
      }

      // Clean up only call-specific socket listeners (not HomeScreen listeners)
      if (callService.socket) {
        callService.socket.off('offer');
        callService.socket.off('answer');
        callService.socket.off('ice-candidate');
        callService.socket.off('call-ended');
        callService.socket.off('call-rejected');
        callService.socket.off('call-accepted');
        callService.socket.off('call-missed');
        callService.socket.off('call-timer-start');
        // Note: Don't remove 'incoming-call' and 'call-missed' as HomeScreen needs them
      }

      if (callId) {
        callService.endCall(callId).catch(err =>
          console.error('Error ending call:', err)
        );

        callService.webrtc.signalEndCall(callId);
      }

      // Don't disconnect socket as HomeScreen needs it for incoming calls
      // callService.disconnectSocket();
    };
  }, []); // Empty dependency array to prevent re-runs

  // Set up audio session for call
  const setupAudioSession = async (): Promise<void> => {
    console.log('🎧 Setting up audio session for call...');

    try {
      console.log('🔊 Audio session setup:');
      console.log('- Setting audio mode to voice call');
      console.log('- Activating audio session');
      console.log('- Configuring audio routing');

      // Use InCallManager to set up audio session
      InCallManagerWrapper.start({
        media: 'audio',       // audio only for voice calls
        auto: true,           // automatically route audio
        ringback: ''          // disable ringback sound - we handle our own ringing
      });

      // Set initial speaker state (off by default for voice calls)
      InCallManagerWrapper.setForceSpeakerphoneOn(false);

      // Set audio mode for voice calls
      InCallManagerWrapper.setKeepScreenOn(true);
      InCallManagerWrapper.setMicrophoneMute(false);

      console.log('✅ Audio session configured for voice call');
    } catch (error) {
      console.error('Error setting up audio session:', error);
    }
  };

  // Clean up audio session
  const cleanupAudioSession = (): void => {
    console.log('🎧 Cleaning up audio session...');

    try {
      // Stop the InCallManager audio session
      InCallManagerWrapper.stop();
      InCallManagerWrapper.setKeepScreenOn(false);
      console.log('✅ Audio session cleaned up');
    } catch (error) {
      console.error('Error cleaning up audio session:', error);
    }
  };

  // Initialize WebRTC
  const initWebRTC = async (callId: string) => {
    try {
      // 1. Check and request audio permissions first
      console.log('Checking audio permissions...');
      const hasPermission = await requestAudioPermissions();
      if (!hasPermission) {
        throw new Error('Audio permission denied');
      }

      // 2. Get user media with enhanced audio configuration
      console.log('Requesting user media with optimized audio settings...');
      const stream = await mediaDevices.getUserMedia({
        audio: true,
        video: false,
      });

      console.log('Got user media stream:', stream.getTracks().length, 'tracks');

      // Verify audio track
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length > 0) {
        console.log('✅ Local audio track:', audioTracks[0].label);
        console.log('🎤 Audio track enabled:', audioTracks[0].enabled);
        console.log('🔊 Audio track state:', audioTracks[0].readyState);
      }

      localStream.current = stream;

      // 2. Create peer connection with improved configuration for stability
      const configuration = {
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' },
          { urls: 'stun:stun2.l.google.com:19302' },
          { urls: 'stun:stun3.l.google.com:19302' },
          { urls: 'stun:stun4.l.google.com:19302' },
          { urls: 'stun:stun.ekiga.net' },
          { urls: 'stun:stun.ideasip.com' },
          { urls: 'stun:stun.stunprotocol.org:3478' },
          // The public TURN server below is often unreliable.
          // For a production app, you should deploy your own COTURN server.
          // {
          //   urls: 'turn:numb.viagenie.ca',
          //   username: '<EMAIL>',
          //   credential: 'muazkh',
          // },
        ],
        iceCandidatePoolSize: 10,
        iceTransportPolicy: 'all' as any,
        bundlePolicy: 'max-bundle' as any,
        rtcpMuxPolicy: 'require' as any,
      };
      peerConnection.current = new RTCPeerConnection(configuration);

      // 3. Add local stream to peer connection
      stream.getTracks().forEach((track: any) => {
        peerConnection.current?.addTrack(track, stream);
      });

      // 4. Set up event handlers for ICE candidates with improved gathering
      peerConnection.current.onicecandidate = (event: RTCIceCandidateEvent) => {
        if (event.candidate && callService.socket) {
          console.log('Sending ICE candidate:', (event.candidate as any).type || 'unknown');
          callService.webrtc.sendIceCandidate(callId, event.candidate);
        } else if (!event.candidate) {
          console.log('ICE candidate gathering completed');
        }
      };

      // Set up ICE gathering state change handler
      (peerConnection.current as any).onicegatheringstatechange = () => {
        const gatheringState = (peerConnection.current as any)?.iceGatheringState;
        console.log('ICE gathering state changed:', gatheringState);
      };

      // 5. Set up event handler for connection state changes
      peerConnection.current.onconnectionstatechange = () => {
        const state = peerConnection.current?.connectionState;
        console.log('Connection state changed:', state);

        switch (state) {
          case 'connected':
            console.log('WebRTC peer connection established successfully');
            setIsConnecting(false);
            setIsCallActive(true);
            break;
          case 'connecting':
            console.log('WebRTC peer connection is connecting...');
            break;
          case 'disconnected':
            console.log('WebRTC peer connection disconnected - waiting for reconnection');
            break;
          case 'failed':
            console.log('WebRTC peer connection failed - attempting ICE restart');
            peerConnection.current?.restartIce();
            break;
          case 'closed':
            console.log('WebRTC peer connection closed');
            break;
        }
      };

      // 6. Set up event handler for ICE connection state changes with improved stability
      let iceFailureCount = 0;
      let iceRestartTimeout: NodeJS.Timeout | null = null;
      let iceConnectionTimeout: NodeJS.Timeout | null = null;

      // Set up ICE connection timeout (15 seconds)
      iceConnectionTimeout = setTimeout(() => {
        const currentIceState = (peerConnection.current as any)?.iceConnectionState;
        if (currentIceState === 'checking' || currentIceState === 'new') {
          console.log('⏰ ICE connection timeout - forcing connection attempt');
          // Force connection by starting timer anyway
          if (!isIncoming) {
            const startTime = Date.now();
            console.log('🕐 Starting timer due to ICE timeout');
            startCallTimer(startTime);
            if (callService.socket) {
              callService.socket.emit('call-timer-start', { callId, startTime });
            }
          }
          setIsConnecting(false);
          setIsCallActive(true);
          soundService.stopRinging();
        }
      }, 15000);

      peerConnection.current.oniceconnectionstatechange = () => {
        const iceState = (peerConnection.current as any)?.iceConnectionState;
        console.log('ICE connection state changed:', iceState);

        if (iceState === 'connected' || iceState === 'completed') {
          iceFailureCount = 0; // Reset failure count on successful connection
          if (iceRestartTimeout) {
            clearTimeout(iceRestartTimeout);
            iceRestartTimeout = null;
          }
          if (iceConnectionTimeout) {
            clearTimeout(iceConnectionTimeout);
            iceConnectionTimeout = null;
          }

          // Stop ringing sound when connection is established
          soundService.stopRinging();
          console.log('🎉 Voice connection established successfully!');

          setIsConnecting(false);
          setIsCallActive(true);

          // Start call timer when connection is established
          if (!isIncoming) {
            const startTime = Date.now();
            console.log('Caller starting synchronized timer at:', startTime);
            startCallTimer(startTime);
            // Emit timer sync to expert
            if (callService.socket) {
              callService.socket.emit('call-timer-start', { callId, startTime });
            }
            // Mark call as connected when ICE is established
            callService.connectToCall(callId);
          }
        } else if (iceState === 'failed') {
          iceFailureCount++;
          console.log('ICE connection failed, attempt:', iceFailureCount);

          // Try ICE restart before giving up
          if (iceFailureCount <= 2 && peerConnection.current) {
            console.log('Attempting ICE restart...');
            iceRestartTimeout = setTimeout(async () => {
              try {
                if (peerConnection.current && !isIncoming) {
                  // Caller initiates ICE restart
                  const offer = await peerConnection.current.createOffer({ iceRestart: true });
                  await peerConnection.current.setLocalDescription(offer);
                  callService.webrtc.sendOffer(callId, offer);
                }
              } catch (error) {
                console.error('ICE restart failed:', error);
              }
            }, 2000);
          } else {
            // Final failure - but allow call to continue without perfect connection
            console.warn('ICE connection failed permanently, but continuing call...');
            soundService.stopRinging();

            // Start timer anyway to allow call to proceed
            if (!isIncoming) {
              const startTime = Date.now();
              console.log('🕐 Starting timer despite ICE failure');
              startCallTimer(startTime);
              if (callService.socket) {
                callService.socket.emit('call-timer-start', { callId, startTime });
              }
              callService.connectToCall(callId);
            }

            setIsConnecting(false);
            setIsCallActive(true);

            // Don't show alert immediately - let the call continue
            console.log('📞 Call continuing despite connection issues...');
          }
        } else if (iceState === 'disconnected') {
          console.log('ICE connection temporarily disconnected - keeping call active');
          // Don't close connection, just log the state change
        } else if (iceState === 'checking') {
          console.log('ICE connection checking...');
        } else if (iceState === 'closed') {
          console.log('ICE connection closed - attempting to maintain call');
          // Don't stop the call timer even if ICE connection closes
          // Try to restart the connection if possible
          if (peerConnection.current && !isIncoming) {
            console.log('🔄 Attempting to restart connection after ICE close');
            setTimeout(async () => {
              try {
                if (peerConnection.current) {
                  const offer = await peerConnection.current.createOffer({ iceRestart: true });
                  await peerConnection.current.setLocalDescription(offer);
                  callService.webrtc.sendOffer(callId, offer);
                }
              } catch (error) {
                console.error('Failed to restart connection:', error);
              }
            }, 2000);
          }
        }
      };

      // 7. Set up event handler for remote stream
      peerConnection.current.ontrack = (event: any) => {
        console.log('Received remote track:', event.track?.kind);
        if (event.streams && event.streams[0]) {
          console.log('🎵 Remote audio stream received - audio should be working!');

          const remoteStream = event.streams[0];
          console.log('🎵 Remote stream tracks:', remoteStream.getTracks().length);

          // Test if audio track is enabled
          const audioTracks = remoteStream.getAudioTracks();
          if (audioTracks.length > 0) {
            const track = audioTracks[0];
            console.log('✅ Audio track found:', track.enabled ? 'enabled' : 'disabled');
            console.log('🔊 Audio track state:', track.readyState);
            console.log('🔊 Audio track ID:', track.id);
            console.log('🔊 Audio track label:', track.label);

            // Ensure the track is enabled
            if (!track.enabled) {
              console.log('🔧 Enabling remote audio track');
              track.enabled = true;
            }
          } else {
            console.warn('❌ No audio tracks found in remote stream');
          }

          // Log all tracks for debugging
          remoteStream.getTracks().forEach((track: any, index: number) => {
            console.log(`🎵 Remote track ${index}: ${track.kind}, enabled: ${track.enabled}, state: ${track.readyState}`);
          });
        } else {
          console.warn('❌ No remote stream received in ontrack event');
        }
      };

      // 7. Initialize socket
      await callService.initSocket();

      console.log('WebRTC initialization completed for', isIncoming ? 'expert' : 'caller');

      // Start connection keep-alive
      startKeepAlive(callId);

    } catch (error) {
      console.error('WebRTC initialization error:', error);
      Alert.alert('Error', 'Failed to initialize call');
      navigation.goBack();
    }
  };

  // Set up socket listeners for WebRTC signaling
  const setupSocketListeners = (callId: string) => {
    if (!callService.socket) return;
    console.log('Setting up socket listeners for', isIncoming ? 'expert' : 'caller');

    // Remove existing listeners to prevent duplicates
    callService.socket.off('offer');
    callService.socket.off('answer');
    callService.socket.off('ice-candidate');
    callService.socket.off('call-ended');
    callService.socket.off('call-rejected');
    callService.socket.off('call-accepted');
    callService.socket.off('call-missed');

    // Listen for answer (for caller) or offer (for expert)
    callService.socket.on('offer', async (data) => {
      console.log('Offer event received, isIncoming:', isIncoming, 'data:', data);
      if (!isIncoming) return; // only expert handles offer
      try {
        if (peerConnection.current && data.offer) {
          console.log('Received offer, setting remote description');
          await peerConnection.current.setRemoteDescription(new RTCSessionDescription(data.offer));

          console.log('Creating answer');
          const answer = await peerConnection.current.createAnswer();
          await peerConnection.current.setLocalDescription(answer);
          console.log('Answer created and local description set');

          console.log('Sending answer');
          callService.webrtc.sendAnswer(callId, answer);

          // For expert: Start timer after sending answer (backup mechanism)
          setTimeout(() => {
            if (isIncoming && callDuration === 0) {
              console.log('🕐 Expert starting backup timer after answer');
              const startTime = Date.now();
              startCallTimer(startTime);
              setIsConnecting(false);
              setIsCallActive(true);
              soundService.forceStopRinging();
              InCallManagerWrapper.stopRingback();
            }
          }, 3000); // Wait 3 seconds for normal timer sync
        } else {
          console.log('Cannot handle offer - peerConnection:', !!peerConnection.current, 'offer:', !!data.offer);
        }
      } catch (err) {
        console.error('Error handling offer:', err);
        Alert.alert('Connection Error', 'Failed to handle incoming call');
      }
    });

    // Listen for answer from expert
    callService.socket.on('answer', async (data) => {
      try {
        if (!peerConnection.current) return;
        if (!data.answer) return;
        if (isIncoming) return; // experts don't process answer

        console.log('Received answer, setting remote description');
        await peerConnection.current.setRemoteDescription(new RTCSessionDescription(data.answer));
        console.log('Remote description set successfully');
        setIsConnecting(false); // Mark as connected when answer is processed
      } catch (error) {
        console.error('Error setting remote description:', error);
        Alert.alert('Connection Error', 'Failed to establish connection');
      }
    });

    // Listen for ICE candidates
    callService.socket.on('ice-candidate', async (data) => {
      try {
        if (peerConnection.current && data.candidate) {
          console.log('Adding ICE candidate');
          await peerConnection.current.addIceCandidate(new RTCIceCandidate(data.candidate));
        }
      } catch (error) {
        console.error('Error adding ICE candidate:', error);
        // Don't show alert for ICE candidate errors as they're common
      }
    });

    // Listen for call end
    callService.socket.on('call-ended', () => {
      endCall();
    });

    // Listen for call rejection (for caller)
    callService.socket.on('call-rejected', ({ message }) => {
      // Stop ringing when call is rejected
      soundService.stopRinging();

      Alert.alert('Call Rejected', message || 'The expert rejected your call', [
        {
          text: 'OK',
          onPress: () => {
            navigation.goBack();
          },
        },
      ]);
    });

    // Listen for call acceptance (for caller)
    callService.socket.on('call-accepted', ({ message }) => {
      console.log('Call accepted:', message);
      // The call is now accepted, WebRTC connection will proceed
    });

    // Listen for call missed
    callService.socket.on('call-missed', ({ message }) => {
      // Stop ringing when call is missed
      soundService.stopRinging();

      Alert.alert('Call Missed', message || 'The call was not answered', [
        {
          text: 'OK',
          onPress: () => {
            navigation.goBack();
          },
        },
      ]);
    });

    // Listen for timer sync events
    callService.socket.on('call-timer-start', ({ startTime }) => {
      console.log('Received timer sync, starting timer with time:', startTime);
      startCallTimer(startTime);
      setIsConnecting(false);
      setIsCallActive(true);
      soundService.forceStopRinging(); // Force stop ringing when timer starts
      InCallManagerWrapper.stopRingback(); // Also stop InCallManager ringback
    });
  };

  // Synchronized call timer
  const startCallTimer = (startTime?: number) => {
    // Prevent multiple timer starts
    if (timerRef.current) {
      console.log('⚠️ Timer already running, not starting again');
      return;
    }

    const actualStartTime = startTime || Date.now();
    console.log('⏰ Starting call timer with timestamp:', actualStartTime, 'Date:', new Date(actualStartTime).toLocaleTimeString());
    setCallStartTime(actualStartTime);
    isCallActiveRef.current = true; // Mark call as active

    // Update duration immediately
    const updateDuration = () => {
      const elapsed = Math.floor((Date.now() - actualStartTime) / 1000);
      setCallDuration(elapsed);
    };

    updateDuration(); // Initial update
    timerRef.current = setInterval(updateDuration, 1000);
  };

  const stopCallTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    isCallActiveRef.current = false; // Mark call as inactive
  };

  // Calculate call cost based on rate
  const calculateCost = () => {
    const minutes = callDuration / 60;
    const rateValue = parseInt(rate.replace(/[^0-9]/g, ''), 10);
    return (minutes * rateValue).toFixed(2);
  };

  const toggleMute = () => {
    try {
      const newMuteState = !isMuted;

      console.log(`${newMuteState ? 'Muting' : 'Unmuting'} microphone`);

      // Update state
      setIsMuted(newMuteState);

      // Use InCallManager to control microphone
      InCallManagerWrapper.setMicrophoneMute(newMuteState);

      // Also control the track directly for better reliability
      if (localStream.current) {
        const audioTracks = localStream.current.getAudioTracks();
        console.log(`Controlling ${audioTracks.length} audio tracks`);

        audioTracks.forEach((track: any) => {
          // When isMuted is true, we want to mute (enable=false)
          // When isMuted is false, we want to unmute (enable=true)
          track.enabled = !newMuteState;
          console.log(`Track ${track.id} ${track.enabled ? 'enabled' : 'disabled'}`);
        });
      } else {
        console.warn('No local stream available, using only InCallManager for mute');
      }
    } catch (error) {
      console.error('Error toggling mute:', error);
    }
  };

  const toggleSpeaker = () => {
    try {
      // Toggle speaker mode
      const newSpeakerState = !isSpeakerOn;
      setIsSpeakerOn(newSpeakerState);

      console.log(`Speaker toggled to: ${newSpeakerState ? 'ON' : 'OFF'}`);

      // Use InCallManager to control speaker
      InCallManagerWrapper.setForceSpeakerphoneOn(newSpeakerState);

      // Force audio track restart to apply changes if needed
      if (localStream.current) {
        const audioTracks = localStream.current.getAudioTracks();
        if (audioTracks.length > 0) {
          const track = audioTracks[0];
          // Briefly disable and re-enable the track to apply routing changes
          const enabled = track.enabled;
          track.enabled = false;
          setTimeout(() => {
            if (track) track.enabled = enabled;
          }, 100);
        }
      }
    } catch (error) {
      console.error('Error toggling speaker:', error);
    }
  };

  const endCall = async () => {
    console.log('🔚 User ending call manually');
    setIsCallActive(false);
    isCallActiveRef.current = false; // Mark call as inactive
    stopCallTimer(); // Stop the timer when call ends
    stopKeepAlive(); // Stop keep-alive

    // Force stop any ringing sounds
    soundService.forceStopRinging();

    // Play call end sound
    soundService.playCallEndSound();

    // Clean up audio session
    cleanupAudioSession();

    // Clean up WebRTC resources
    if (localStream.current) {
      console.log('Stopping all local audio tracks...');
      localStream.current.getTracks().forEach((track: any) => {
        console.log(`Stopping track: ${track.kind} (${track.id})`);
        track.stop();
      });
      localStream.current = null;
    }

    if (peerConnection.current) {
      console.log('Closing peer connection...');
      try {
        // Remove all event listeners
        peerConnection.current.onicecandidate = null;
        peerConnection.current.onconnectionstatechange = null;
        peerConnection.current.oniceconnectionstatechange = null;
        peerConnection.current.ontrack = null;

        // Close the connection
        peerConnection.current.close();
        peerConnection.current = null;
      } catch (error) {
        console.error('Error closing peer connection:', error);
      }
    }

    if (callId) {
      try {
        console.log('Sending end call signal to server...');
        await callService.endCall(callId);
        callService.webrtc.signalEndCall(callId);
        setCallId(null);
      } catch (error) {
        console.error('Error ending call:', error);
      }
    }

    // Short delay before navigation to ensure cleanup completes
    setTimeout(() => {
      navigation.goBack();
    }, 300);
  };

  return (
    <LinearGradient
      colors={['#f4bccd', '#f1eec8', '#d3eef4']}
      locations={[0, 0.3, 1]}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
      style={styles.container}
    >
      <SafeAreaView style={styles.container}>
        {/* Call info */}
        <View style={styles.callInfoContainer}>
          <Text style={styles.callStatus}>
            {isConnecting ? 'Connecting...' : 'Call in progress'}
          </Text>
          <Text style={styles.timer}>{formatTime(callDuration)}</Text>
          <Text style={styles.cost}>₹{calculateCost()}</Text>
        </View>

        {/* User profile */}
        <View style={styles.profileContainer}>
          {isConnecting && (
            <ActivityIndicator size="large" color="#3952f5" style={styles.connectingIndicator} />
          )}
          <Image
            source={require('../../assets/images/user.png')}
            style={styles.profileImage}
          />
          <Text style={styles.userName}>{userName}</Text>
          <Text style={styles.userCategory}>{category}</Text>
          <Text style={styles.userRate}>{rate}</Text>
        </View>

        {/* Call controls */}
        <View style={styles.controlsContainer}>
          <TouchableOpacity
            style={[styles.controlButton, isMuted && styles.activeControl]}
            onPress={toggleMute}
            disabled={isConnecting}
          >
            {isMuted ? <MicOff size={24} color="#fff" /> : <Mic size={24} color="#fff" />}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.endCallButton}
            onPress={endCall}
          >
            <PhoneOff size={28} color="#fff" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.controlButton, isSpeakerOn && styles.activeControl]}
            onPress={toggleSpeaker}
            disabled={isConnecting}
          >
            <Volume2 size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  callInfoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  callStatus: {
    fontSize: 16,
    color: '#333',
    fontFamily: 'Lato-Regular',
  },
  timer: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#000',
    marginTop: 8,
    fontFamily: 'Poppins-Bold',
  },
  cost: {
    fontSize: 18,
    color: '#f40752',
    marginTop: 8,
    fontFamily: 'Lato-Bold',
  },
  profileContainer: {
    alignItems: 'center',
  },
  connectingIndicator: {
    position: 'absolute',
    top: -40,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 20,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000',
    fontFamily: 'Poppins-Bold',
  },
  userCategory: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
    textTransform: 'capitalize',
    fontFamily: 'Lato-Regular',
  },
  userRate: {
    fontSize: 16,
    color: '#333',
    marginTop: 5,
    fontFamily: 'Lato-Bold',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    marginBottom: 40,
    paddingHorizontal: 20,
  },
  controlButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 50,
    width: 60,
    height: 60,
  },
  activeControl: {
    backgroundColor: '#3952f5',
  },
  endCallButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f40752',
    borderRadius: 50,
    width: 70,
    height: 70,
  },
  controlText: {
    color: '#fff',
    fontSize: 12,
    marginTop: 5,
    fontFamily: 'Lato-Regular',
  },
});

export default CallScreen;
